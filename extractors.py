from bs4 import BeautifulSoup
import re
import os
from typing import Dict, List, Optional
from email_processor import EmailProcessor

class DataExtractors:
    
    def __init__(self):
        self.email_processor = EmailProcessor()
    
    def extract_from_file(self, file_path: str) -> Dict:
        """Main extraction method that routes to appropriate extractor"""
        try:
            print(f"Starting extraction for: {file_path}")
            
            file_ext = os.path.splitext(file_path)[1].lower()
            filename = os.path.basename(file_path).lower()
            
            print(f"File extension: {file_ext}, Filename: {filename}")
            
            # Determine file type and extract accordingly
            if file_ext == '.html':
                if 'contact_form' in filename or 'form' in filename:
                    print("Processing as form")
                    return self._extract_from_form(file_path)
                elif 'invoice' in filename or 'tf-' in filename:
                    print("Processing as invoice")
                    return self._extract_from_invoice(file_path)
                else:
                    # Try to determine by content
                    print("Determining HTML type by content")
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().lower()
                    
                    if 'τιμολόγιο' in content or 'invoice' in content or 'tf-' in content:
                        print("Content indicates invoice")
                        return self._extract_from_invoice(file_path)
                    else:
                        print("Content indicates form")
                        return self._extract_from_form(file_path)
            
            elif file_ext == '.eml':
                print("Processing as email")
                return self._extract_from_email(file_path)
            
            else:
                error_msg = f'Μη υποστηριζόμενος τύπος αρχείου: {file_ext}'
                print(error_msg)
                return {'error': error_msg}
                
        except Exception as e:
            error_msg = f'Σφάλμα επεξεργασίας αρχείου {file_path}: {str(e)}'
            print(error_msg)
            return {'error': error_msg}
    
    def _extract_from_form(self, file_path: str) -> Dict:
        """Extract data from HTML contact forms"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            extracted_data = {
                'type': 'FORM',
                'source_file': os.path.basename(file_path)
            }
            
            # Extract form fields using multiple strategies
            form_fields = {
                'full_name': 'client_name',
                'email': 'email',
                'phone': 'phone',
                'company': 'company',
                'service': 'service_interest',
                'message': 'message',
                'priority': 'priority',
                'submission_date': 'submission_date'
            }
            
            for field_name, db_field in form_fields.items():
                value = self._extract_form_field(soup, field_name)
                if value:
                    extracted_data[db_field] = value
            
            # Clean and validate extracted data
            extracted_data = self._clean_form_data(extracted_data)
            
            return extracted_data
            
        except Exception as e:
            return {'error': f'Σφάλμα ανάλυσης φόρμας: {str(e)}'}
    
    def _extract_form_field(self, soup: BeautifulSoup, field_name: str) -> Optional[str]:
        """Extract specific field from form using multiple strategies"""
        # Strategy 1: Direct name attribute match
        selectors = [
            f'input[name="{field_name}"]',
            f'input[name*="{field_name}"]',
            f'select[name="{field_name}"]',
            f'textarea[name="{field_name}"]'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                if element.name == 'select':
                    selected = element.select_one('option[selected]')
                    if selected:
                        return selected.get('value') or selected.get_text().strip()
                elif element.name == 'textarea':
                    return element.get_text().strip() or element.get('value', '').strip()
                else:
                    return element.get('value', '').strip()
        
        # Strategy 2: Search by field patterns in the whole document
        text_content = soup.get_text()
        
        if field_name == 'full_name':
            patterns = [
                r'Όνομα και Επώνυμο[:\s]*([Α-Ωα-ωA-Za-z\s]+)',
                r'value="([Α-Ωα-ωA-Za-z\s]+)".*readonly'
            ]
        elif field_name == 'email':
            patterns = [
                r'value="([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})"'
            ]
        elif field_name == 'phone':
            patterns = [
                r'value="([0-9\-\+\s]+)"'
            ]
        elif field_name == 'company':
            patterns = [
                r'Εταιρεία[:\s]*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
                r'value="([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)".*company'
            ]
        elif field_name == 'service':
            patterns = [
                r'Υπηρεσία Ενδιαφέροντος[:\s]*([Α-Ωα-ωA-Za-z\s]+)',
                r'option value="[^"]*" selected[^>]*>([^<]+)'
            ]
        elif field_name == 'priority':
            patterns = [
                r'Προτεραιότητα[:\s]*([Α-Ωα-ωA-Za-z]+)',
                r'option value="([^"]*)" selected[^>]*>.*?(?:Υψηλή|Μέτρια|Χαμηλή)'
            ]
        elif field_name == 'submission_date':
            patterns = [
                r'value="([0-9\-T:]+)".*datetime-local'
            ]
        elif field_name == 'message':
            patterns = [
                r'<textarea[^>]*>([^<]+)</textarea>'
            ]
        else:
            return None
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
        
        return None
    
    def _clean_form_data(self, data: Dict) -> Dict:
        """Clean and validate form data"""
        # Convert service codes to readable names
        service_mapping = {
            'web_development': 'Ανάπτυξη Website',
            'database_management': 'Διαχείριση Βάσης Δεδομένων',
            'pos_system': 'Σύστημα POS',
            'healthcare_system': 'Σύστημα Διαχείρισης Ιατρείου',
            'project_management': 'Σύστημα Διαχείρισης Έργων'
        }
        
        if 'service_interest' in data and data['service_interest'] in service_mapping:
            data['service_interest'] = service_mapping[data['service_interest']]
        
        # Convert priority codes
        priority_mapping = {
            'high': 'Υψηλή',
            'medium': 'Μέτρια',
            'low': 'Χαμηλή'
        }
        
        if 'priority' in data and data['priority'] in priority_mapping:
            data['priority'] = priority_mapping[data['priority']]
        
        # Clean phone numbers
        if 'phone' in data and data['phone']:
            data['phone'] = re.sub(r'[^\d\-\+\s]', '', data['phone'])
        
        return data
    
    def _extract_from_invoice(self, file_path: str) -> Dict:
        """Extract data from HTML invoices"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            text_content = soup.get_text()
            
            extracted_data = {
                'type': 'INVOICE',
                'source_file': os.path.basename(file_path)
            }
            
            # Extract invoice number
            invoice_number = self._extract_invoice_number(text_content)
            if invoice_number:
                extracted_data['invoice_number'] = invoice_number
            
            # Extract date
            date = self._extract_invoice_date(text_content)
            if date:
                extracted_data['invoice_date'] = date
            
            # Extract client name
            client_name = self._extract_invoice_client_name(text_content)
            if client_name:
                extracted_data['client_name'] = client_name
            
            # Extract financial data
            amounts = self._extract_invoice_amounts(text_content)
            extracted_data.update(amounts)
            
            # Extract items/services
            items = self._extract_invoice_items(soup)
            if items:
                extracted_data['message'] = items
            
            return extracted_data
            
        except Exception as e:
            return {'error': f'Σφάλμα ανάλυσης τιμολογίου: {str(e)}'}
    
    def _extract_invoice_number(self, content: str) -> Optional[str]:
        """Extract invoice number"""
        patterns = [
            r'Αριθμός.*?[:]\s*([A-Z]{2}-\d{4}-\d{3})',
            r'ΤΙΜΟΛΟΓΙΟ.*?([A-Z]{2}-\d{4}-\d{3})',
            r'([A-Z]{2}-\d{4}-\d{3})',
            r'Τιμολόγιο\s+([A-Z]{2}-\d{4}-\d{3})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1) if len(match.groups()) >= 1 else match.group(0)
        
        return None
    
    def _extract_invoice_date(self, content: str) -> Optional[str]:
        """Extract invoice date"""
        patterns = [
            r'Ημερομηνία.*?[:]\s*(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_invoice_client_name(self, content: str) -> Optional[str]:
        """Extract client name from invoice"""
        patterns = [
            r'Πελάτης.*?[:]\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+?)(?:\n|Διεύθυνση|ΑΦΜ)',
            r'Προμηθευτής.*?[:]\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+?)(?:\n|Διεύθυνση|ΑΦΜ)',
            r'<strong>Πελάτης:</strong><br>\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'Πελάτης:</strong><br>\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.MULTILINE | re.IGNORECASE)
            if match:
                client_name = match.group(1).strip()
                if len(client_name) > 2:
                    return client_name
        
        return None
    
    def _extract_invoice_amounts(self, content: str) -> Dict:
        """Extract financial amounts from invoice"""
        amounts = {}
        
        # Extract net amount (Καθαρή Αξία)
        net_patterns = [
            r'Καθαρή Αξία.*?[:]\s*€\s*([0-9,]+\.?\d*)',
            r'€\s*([0-9,]+\.?\d*).*?Καθαρή',
            r'<strong>Καθαρή Αξία:</strong>.*?€([0-9,]+\.?\d*)'
        ]
        
        for pattern in net_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                try:
                    amounts['amount'] = float(match.group(1).replace(',', ''))
                    break
                except ValueError:
                    continue
        
        # Extract VAT (ΦΠΑ)
        vat_patterns = [
            r'ΦΠΑ.*?[:]\s*€\s*([0-9,]+\.?\d*)',
            r'€\s*([0-9,]+\.?\d*).*?ΦΠΑ',
            r'<strong>ΦΠΑ.*?:</strong>.*?€([0-9,]+\.?\d*)'
        ]
        
        for pattern in vat_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                try:
                    amounts['vat'] = float(match.group(1).replace(',', ''))
                    break
                except ValueError:
                    continue
        
        # Extract total (ΣΥΝΟΛΟ)
        total_patterns = [
            r'ΣΥΝΟΛΟ.*?[:]\s*€\s*([0-9,]+\.?\d*)',
            r'Συνολικό Ποσό.*?[:]\s*€\s*([0-9,]+\.?\d*)',
            r'€\s*([0-9,]+\.?\d*).*?ΣΥΝΟΛΟ',
            r'<strong>ΣΥΝΟΛΟ:</strong>.*?€([0-9,]+\.?\d*)'
        ]
        
        for pattern in total_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                try:
                    amounts['total_amount'] = float(match.group(1).replace(',', ''))
                    break
                except ValueError:
                    continue
        
        return amounts
    
    def _extract_invoice_items(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract items/services from invoice table"""
        try:
            # Find invoice table
            table = soup.find('table', class_='invoice-table') or soup.find('table')
            
            if not table:
                return None
            
            items = []
            rows = table.find_all('tr')
            
            for row in rows[1:]:  # Skip header
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    description = cells[0].get_text().strip()
                    if description and not description.lower().startswith(('καθαρή', 'φπα', 'σύνολο')):
                        # Get quantity and price if available
                        item_info = description
                        if len(cells) >= 3:
                            qty = cells[1].get_text().strip()
                            price = cells[2].get_text().strip() if len(cells) > 2 else ""
                            if qty and price:
                                item_info += f" ({qty} x {price})"
                        items.append(item_info)
            
            return '; '.join(items[:5]) if items else None
            
        except Exception:
            return None
    
    def _extract_from_email(self, file_path: str) -> Dict:
        """Extract data from email files using EmailProcessor"""
        try:
            result = self.email_processor.parse_eml_file(file_path)
            if 'error' not in result:
                result['source_file'] = os.path.basename(file_path)
            return result
        except Exception as e:
            return {'error': f'Σφάλμα ανάλυσης email: {str(e)}'}
    
    def process_folder(self, folder_path: str) -> List[Dict]:
        """Process all files in a folder and return extracted data"""
        results = []
        
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # Skip hidden files and unsupported formats
                    if file.startswith('.'):
                        continue
                    
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext not in ['.html', '.eml']:
                        continue
                    
                    extracted_data = self.extract_from_file(file_path)
                    
                    # Add metadata
                    extracted_data['original_path'] = file_path
                    extracted_data['file_size'] = os.path.getsize(file_path)
                    
                    results.append(extracted_data)
            
            return results
            
        except Exception as e:
            return [{'error': f'Σφάλμα επεξεργασίας φακέλου: {str(e)}'}]