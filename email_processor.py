import email
import email.message
import re
from datetime import datetime
from typing import Dict, Optional, Tuple

class EmailProcessor:
    
    def __init__(self):
        # Keywords to identify invoice emails - based on actual dummy data
        self.invoice_keywords = [
            'τιμολόγιο', 'invoice', 'λογιστήριο', 'accounting', 'TF-',
            'καθαρή αξία', 'φπα', 'σύνολο', 'προμηθευτής', 'attached'
        ]
        
        # Keywords to identify client inquiry emails
        self.inquiry_keywords = [
            'αίτημα', 'request', 'χρειαζόμαστε', 'θέλουμε', 'ενδιαφέρον',
            'σύστημα', 'υπηρεσία', 'λύση', 'website', 'application', 'συνάντηση',
            'ονομάζομαι', 'είμαι ο', 'είμαι η'
        ]
    
    def parse_eml_file(self, file_path: str) -> Dict:
        """Parse EML file and extract all relevant data"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            msg = email.message_from_string(content)
            
            # Basic email data
            email_data = {
                'from': msg.get('From', ''),
                'to': msg.get('To', ''),
                'subject': msg.get('Subject', ''),
                'date': msg.get('Date', ''),
                'body': self._get_email_body(msg)
            }
            
            # Categorize email type
            email_type, confidence = self._categorize_email(email_data)
            
            # Extract data based on type
            if email_type == 'invoice_notification':
                extracted_data = self._extract_invoice_data(email_data)
            else:
                extracted_data = self._extract_client_data(email_data)
            
            extracted_data.update({
                'type': 'EMAIL',
                'email_type': email_type,
                'email_subject': email_data['subject'],
                'email_date': email_data['date'],
                'confidence': confidence
            })
            
            return extracted_data
            
        except Exception as e:
            return {'error': f'Σφάλμα ανάλυσης email: {str(e)}'}
    
    def _get_email_body(self, msg) -> str:
        """Extract email body text"""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    payload = part.get_payload(decode=True)
                    if payload:
                        return payload.decode('utf-8', errors='ignore')
        else:
            payload = msg.get_payload(decode=True)
            if payload:
                return payload.decode('utf-8', errors='ignore')
        return ""
    
    def _categorize_email(self, email_data: Dict) -> Tuple[str, float]:
        """Categorize email as invoice or client inquiry"""
        content = f"{email_data['subject']} {email_data['body']}".lower()
        
        invoice_score = sum(1 for keyword in self.invoice_keywords if keyword.lower() in content)
        inquiry_score = sum(1 for keyword in self.inquiry_keywords if keyword.lower() in content)
        
        # Check for invoice patterns
        if re.search(r'τιμολόγιο.*#?[A-Z]{2}-\d{4}-\d{3}', content):
            invoice_score += 3
        
        if re.search(r'€\s*\d+[,.]?\d*', content):
            invoice_score += 1
            
        if 'λογιστήριο' in content:
            invoice_score += 2
            
        if 'attachment' in content or 'attached' in content:
            invoice_score += 1
        
        # Check for client data patterns
        if re.search(r'όνομα.*επώνυμο|name|ονομάζομαι|είμαι', content):
            inquiry_score += 2
        
        if re.search(r'εταιρεία|company', content):
            inquiry_score += 1
            
        if re.search(r'τηλέφωνο|κινητό|phone', content):
            inquiry_score += 1
        
        if invoice_score > inquiry_score:
            confidence = min(0.9, 0.5 + (invoice_score - inquiry_score) * 0.1)
            return 'invoice_notification', confidence
        else:
            confidence = min(0.9, 0.5 + (inquiry_score - invoice_score) * 0.1)
            return 'client_inquiry', confidence
    
    def _extract_invoice_data(self, email_data: Dict) -> Dict:
        """Extract financial data from invoice emails"""
        content = email_data['body']
        subject = email_data['subject']
        
        extracted = {
            'client_name': self._extract_invoice_client(content),
            'invoice_number': self._extract_invoice_number(content + " " + subject),
            'amount': self._extract_amount(content, 'καθαρή αξία'),
            'vat': self._extract_amount(content, 'φπα'),
            'total_amount': self._extract_amount(content, 'συνολικό ποσό|σύνολο'),
            'message': self._extract_invoice_items(content)
        }
        
        return {k: v for k, v in extracted.items() if v is not None}
    
    def _extract_client_data(self, email_data: Dict) -> Dict:
        """Extract client information from inquiry emails"""
        content = email_data['body']
        from_header = email_data['from']
        
        extracted = {
            'client_name': self._extract_client_name(content, from_header),
            'email': self._extract_email_address(content, from_header),
            'phone': self._extract_phone(content),
            'company': self._extract_company(content),
            'service_interest': self._extract_service_interest(content),
            'priority': self._extract_priority(content),
            'message': self._extract_client_message(content)
        }
        
        return {k: v for k, v in extracted.items() if v is not None}
    
    def _extract_invoice_number(self, content: str) -> Optional[str]:
        """Extract invoice number from content"""
        patterns = [
            r'αριθμός.*?([A-Z]{2}-\d{4}-\d{3})',
            r'τιμολόγιο.*?#?([A-Z]{2}-\d{4}-\d{3})',
            r'([A-Z]{2}-\d{4}-\d{3})',
            r'#([A-Z]{2}-\d{4}-\d{3})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None
    
    def _extract_amount(self, content: str, label: str) -> Optional[float]:
        """Extract monetary amounts"""
        patterns = [
            rf'{label}.*?€\s*([0-9,]+\.?\d*)',
            rf'€\s*([0-9,]+\.?\d*).*?{label}'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                amount_str = match.group(1).replace(',', '')
                try:
                    return float(amount_str)
                except ValueError:
                    continue
        return None
    
    def _extract_client_name(self, content: str, from_header: str) -> Optional[str]:
        """Extract client name from email content"""
        # Try from content first
        patterns = [
            r'όνομα.*?[:]\s*([Α-Ωα-ωA-Za-z\s]+)',
            r'είμαι ο\s+([Α-Ωα-ωA-Za-z\s]+)',
            r'ονομάζομαι\s+([Α-Ωα-ωA-Za-z\s]+)',
            r'είμαι η\s+([Α-Ωα-ωA-Za-z\s]+)',
            r'-\s+Όνομα:\s+([Α-Ωα-ωA-Za-z\s]+)',
            r'Όνομα:\s+([Α-Ωα-ωA-Za-z\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.UNICODE)
            if match:
                name = match.group(1).strip()
                # Clean up the name
                name = re.sub(r'[<>@\.].*', '', name)
                if len(name) > 2 and not any(char.isdigit() for char in name):
                    return name
        
        # Try from From header as fallback
        if from_header:
            # <AUTHOR> <EMAIL>" format
            name_match = re.search(r'^([^<]+)', from_header)
            if name_match:
                name = name_match.group(1).strip()
                # Remove common Greek characters that might be email addresses
                if '@' not in name and len(name) > 2:
                    return name
        
        return None
    
    def _extract_email_address(self, content: str, from_header: str) -> Optional[str]:
        """Extract email address"""
        # Try from content first
        patterns = [
            r'email.*?[:]\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'Email:\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
            r'-\s+Email:\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        
        # Try from From header as fallback
        if from_header:
            email_match = re.search(r'<([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})>', from_header)
            if email_match:
                return email_match.group(1)
            
            # Direct email format
            email_match = re.search(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', from_header)
            if email_match:
                return email_match.group(1)
        
        return None
    
    def _extract_phone(self, content: str) -> Optional[str]:
        """Extract phone number"""
        patterns = [
            r'τηλέφωνο.*?[:]\s*([0-9\-\s\+]+)',
            r'κινητό.*?[:]\s*([0-9\-\s\+]+)',
            r'phone.*?[:]\s*([0-9\-\s\+]+)',
            r'Τηλέφωνο:\s*([0-9\-\s\+]+)',
            r'-\s+Τηλέφωνο:\s*([0-9\-\s\+]+)',
            r'(\+?30)?[-\s]?([0-9]{3,4}[-\s]?[0-9]{6,7})',
            r'(69[0-9]{8})',
            r'(210-[0-9]{7})',
            r'(2310-[0-9]{6})',
            r'(22840-[0-9]{6})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                if len(match.groups()) == 1:
                    phone = match.group(1)
                else:
                    phone = match.group(0)
                phone = re.sub(r'[^\d\+\-]', '', phone.strip())
                if len(phone) >= 10:
                    return phone
        return None
    
    def _extract_company(self, content: str) -> Optional[str]:
        """Extract company name"""
        patterns = [
            r'εταιρεία.*?[:]\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'company.*?[:]\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'από την\s+([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'στην\s+([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'Εταιρεία:\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'-\s+Εταιρεία:\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'([Α-Ωα-ωA-Za-z\s&\.\-]+)\s+(?:AE|ΑΕ|Ltd|SA|ΣΑ)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.UNICODE)
            if match:
                company = match.group(1).strip()
                # Clean up common endings and validate
                company = re.sub(r'\s+(και|AE|ΑΕ|Ltd|SA|ΣΑ).*', '', company)
                if len(company) > 2 and not any(char.isdigit() for char in company[:3]):
                    return company
        return None
    
    def _extract_service_interest(self, content: str) -> Optional[str]:
        """Extract service of interest"""
        services = {
            'crm': 'CRM System',
            'e-commerce': 'E-commerce Platform',
            'garage': 'Garage Management System',
            'hotel': 'Hotel Management System',
            'pharmacy': 'Pharmacy Management System',
            'food delivery': 'Food Delivery App',
            'website': 'Website Development',
            'pos': 'POS System'
        }
        
        content_lower = content.lower()
        for key, service in services.items():
            if key in content_lower:
                return service
        
        # Generic fallback
        if 'σύστημα' in content_lower:
            return 'Management System'
        if 'διαχείριση' in content_lower:
            return 'Management System'
            
        return None
    
    def _extract_priority(self, content: str) -> Optional[str]:
        """Extract or determine priority"""
        if any(word in content.lower() for word in ['επείγον', 'urgent', 'άμεσα', 'επείγουσα']):
            return 'Υψηλή'
        elif any(word in content.lower() for word in ['χαμηλή', 'low']):
            return 'Χαμηλή'
        else:
            return 'Μέτρια'
    
    def _extract_client_message(self, content: str) -> Optional[str]:
        """Extract the main message/request"""
        lines = content.split('\n')
        message_lines = []
        
        # Skip header-like lines and extract meaningful content
        for line in lines:
            line = line.strip()
            if len(line) > 20 and not line.startswith(('From:', 'To:', 'Subject:', 'Date:')):
                if not re.match(r'^[Α-Ω][α-ω]+.*?[:]\s*', line):  # Skip field labels
                    if not line.startswith('-') and '@' not in line:
                        message_lines.append(line)
        
        if message_lines:
            # Return first meaningful paragraph
            return ' '.join(message_lines[:2])
        return None
    
    def _extract_invoice_client(self, content: str) -> Optional[str]:
        """Extract client name from invoice email"""
        patterns = [
            r'προμηθευτής.*?[:]\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'πελάτης.*?[:]\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)',
            r'Προμηθευτής:\s*([Α-Ωα-ωA-Za-z\s&\.\-\(\)]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.UNICODE)
            if match:
                client = match.group(1).strip()
                if len(client) > 2:
                    return client
        return None
    
    def _extract_invoice_items(self, content: str) -> Optional[str]:
        """Extract invoice items description"""
        lines = content.split('\n')
        items = []
        
        for line in lines:
            if '€' in line and any(char.isdigit() for char in line):
                # Clean up the line
                line = line.strip()
                if len(line) > 10:
                    items.append(line)
        
        return '; '.join(items[:5]) if items else None