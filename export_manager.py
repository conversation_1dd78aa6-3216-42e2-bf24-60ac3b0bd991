import pandas as pd
import os
from datetime import datetime
from typing import List, Dict
from database import ExtractedData

class ExportManager:
    
    def __init__(self):
        self.template_columns = [
            'Type', 'Source', 'Date', 'Client_Name', 'Email', 'Phone', 
            'Company', 'Service_Interest', 'Amount', 'VAT', 'Total_Amount', 
            'Invoice_Number', 'Priority', 'Message'
        ]
        
        self.greek_columns = [
            'Τύπος', 'Πηγή', 'Ημερομηνία', 'Όνομα Πελάτη', 'Email', 'Τηλέφωνο',
            'Εταιρεία', 'Υπηρεσία Ενδιαφέροντος', 'Ποσό', 'ΦΠΑ', 'Συνολικό Ποσό',
            'Αριθμός Τιμολογίου', 'Προτεραιότητα', 'Μήνυμα'
        ]
    
    def export_to_excel(self, approved_records: List[ExtractedData], template_format: bool = True) -> str:
        """Export approved records to Excel file"""
        try:
            # Prepare data for export
            export_data = []
            
            for record in approved_records:
                row_data = self._prepare_record_for_export(record, template_format)
                export_data.append(row_data)
            
            # Create DataFrame
            if template_format:
                df = pd.DataFrame(export_data, columns=self.template_columns)
            else:
                df = pd.DataFrame(export_data)
                df.columns = self.greek_columns[:len(df.columns)]
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extracted_data_{timestamp}.xlsx"
            filepath = os.path.join("exports", filename)
            
            # Ensure exports directory exists
            os.makedirs("exports", exist_ok=True)
            
            # Export to Excel with formatting
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Εξαχθέντα Δεδομένα', index=False)
                
                # Get workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Εξαχθέντα Δεδομένα']
                
                # Apply formatting
                self._format_excel_sheet(workbook, worksheet, df)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"Σφάλμα εξαγωγής Excel: {str(e)}")
    
    def _prepare_record_for_export(self, record: ExtractedData, template_format: bool) -> Dict:
        """Prepare a single record for export"""
        if template_format:
            return {
                'Type': record.type,
                'Source': record.source_file,
                'Date': record.date_extracted.strftime('%Y-%m-%d') if record.date_extracted else '',
                'Client_Name': record.client_name or '',
                'Email': record.email or '',
                'Phone': record.phone or '',
                'Company': record.company or '',
                'Service_Interest': record.service_interest or '',
                'Amount': record.amount or '',
                'VAT': record.vat or '',
                'Total_Amount': record.total_amount or '',
                'Invoice_Number': record.invoice_number or '',
                'Priority': record.priority or '',
                'Message': record.message or ''
            }
        else:
            return {
                'Τύπος': self._translate_type(record.type),
                'Πηγή': record.source_file,
                'Ημερομηνία': record.date_extracted.strftime('%d/%m/%Y') if record.date_extracted else '',
                'Όνομα Πελάτη': record.client_name or '',
                'Email': record.email or '',
                'Τηλέφωνο': record.phone or '',
                'Εταιρεία': record.company or '',
                'Υπηρεσία Ενδιαφέροντος': record.service_interest or '',
                'Ποσό (€)': f"€{record.amount:.2f}" if record.amount else '',
                'ΦΠΑ (€)': f"€{record.vat:.2f}" if record.vat else '',
                'Συνολικό Ποσό (€)': f"€{record.total_amount:.2f}" if record.total_amount else '',
                'Αριθμός Τιμολογίου': record.invoice_number or '',
                'Προτεραιότητα': record.priority or '',
                'Μήνυμα': record.message or ''
            }
    
    def _translate_type(self, record_type: str) -> str:
        """Translate record type to Greek"""
        translations = {
            'FORM': 'Φόρμα',
            'EMAIL': 'Email',
            'INVOICE': 'Τιμολόγιο'
        }
        return translations.get(record_type, record_type)
    
    def _format_excel_sheet(self, workbook, worksheet, df):
        """Apply formatting to Excel sheet"""
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Border style
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Format headers
        for col_num, column in enumerate(df.columns, 1):
            cell = worksheet.cell(row=1, column=col_num)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = thin_border
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Apply borders to all cells
        for row in worksheet.iter_rows():
            for cell in row:
                cell.border = thin_border
        
        # Freeze first row
        worksheet.freeze_panes = "A2"
    
    def export_summary_report(self, all_records: List[ExtractedData]) -> str:
        """Generate summary report"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summary_report_{timestamp}.xlsx"
            filepath = os.path.join("exports", filename)
            
            # Ensure exports directory exists
            os.makedirs("exports", exist_ok=True)
            
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # Summary statistics
                summary_data = self._generate_summary_stats(all_records)
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Περίληψη', index=False)
                
                # Records by type
                type_data = self._generate_type_breakdown(all_records)
                type_df = pd.DataFrame(type_data)
                type_df.to_excel(writer, sheet_name='Ανά Τύπο', index=False)
                
                # Financial summary (for invoices)
                financial_data = self._generate_financial_summary(all_records)
                if financial_data:
                    financial_df = pd.DataFrame(financial_data)
                    financial_df.to_excel(writer, sheet_name='Οικονομικά Στοιχεία', index=False)
            
            return filepath
            
        except Exception as e:
            raise Exception(f"Σφάλμα δημιουργίας αναφοράς: {str(e)}")
    
    def _generate_summary_stats(self, records: List[ExtractedData]) -> List[Dict]:
        """Generate summary statistics"""
        total_records = len(records)
        approved_records = len([r for r in records if r.status == 'approved'])
        pending_records = len([r for r in records if r.status == 'pending'])
        rejected_records = len([r for r in records if r.status == 'rejected'])
        
        forms_count = len([r for r in records if r.type == 'FORM'])
        emails_count = len([r for r in records if r.type == 'EMAIL'])
        invoices_count = len([r for r in records if r.type == 'INVOICE'])
        
        return [
            {'Στατιστικό': 'Συνολικά Εγγραφές', 'Αριθμός': total_records},
            {'Στατιστικό': 'Εγκεκριμένες', 'Αριθμός': approved_records},
            {'Στατιστικό': 'Σε Αναμονή', 'Αριθμός': pending_records},
            {'Στατιστικό': 'Απορριφθείσες', 'Αριθμός': rejected_records},
            {'Στατιστικό': '', 'Αριθμός': ''},
            {'Στατιστικό': 'Φόρμες', 'Αριθμός': forms_count},
            {'Στατιστικό': 'Emails', 'Αριθμός': emails_count},
            {'Στατιστικό': 'Τιμολόγια', 'Αριθμός': invoices_count},
        ]
    
    def _generate_type_breakdown(self, records: List[ExtractedData]) -> List[Dict]:
        """Generate breakdown by record type"""
        breakdown = {}
        
        for record in records:
            key = f"{record.type} - {record.status}"
            breakdown[key] = breakdown.get(key, 0) + 1
        
        return [{'Τύπος': k, 'Αριθμός': v} for k, v in breakdown.items()]
    
    def _generate_financial_summary(self, records: List[ExtractedData]) -> List[Dict]:
        """Generate financial summary for invoices"""
        invoices = [r for r in records if r.type == 'INVOICE' and r.amount is not None]
        
        if not invoices:
            return []
        
        total_amount = sum(r.amount for r in invoices if r.amount)
        total_vat = sum(r.vat for r in invoices if r.vat)
        total_with_vat = sum(r.total_amount for r in invoices if r.total_amount)
        
        approved_invoices = [r for r in invoices if r.status == 'approved']
        approved_amount = sum(r.amount for r in approved_invoices if r.amount)
        
        return [
            {'Στοιχείο': 'Συνολικά Τιμολόγια', 'Ποσό (€)': len(invoices)},
            {'Στοιχείο': 'Καθαρή Αξία', 'Ποσό (€)': f"€{total_amount:.2f}"},
            {'Στοιχείο': 'ΦΠΑ', 'Ποσό (€)': f"€{total_vat:.2f}"},
            {'Στοιχείο': 'Σύνολο με ΦΠΑ', 'Ποσό (€)': f"€{total_with_vat:.2f}"},
            {'Στοιχείο': '', 'Ποσό (€)': ''},
            {'Στοιχείο': 'Εγκεκριμένα Τιμολόγια', 'Ποσό (€)': len(approved_invoices)},
            {'Στοιχείο': 'Εγκεκριμένη Αξία', 'Ποσό (€)': f"€{approved_amount:.2f}"},
        ]
    
    def create_google_sheets_format(self, approved_records: List[ExtractedData]) -> Dict:
        """Prepare data for Google Sheets API format"""
        headers = self.greek_columns
        
        rows = [headers]
        for record in approved_records:
            row_data = self._prepare_record_for_export(record, template_format=False)
            row = [row_data.get(col, '') for col in headers]
            rows.append(row)
        
        return {
            'values': rows,
            'majorDimension': 'ROWS'
        }