// Application State
let socket = null;
let pendingData = [];
let selectedRecords = new Set();
let currentTab = 'dashboard';

// DOM Elements - Updated for new structure
const elements = {
    // Navigation
    navLinks: document.querySelectorAll('.nav-link'),
    sidebarToggle: document.getElementById('sidebar-toggle'),
    sidebar: document.querySelector('.sidebar'),
    
    // Upload elements
    uploadArea: document.getElementById('upload-area'),
    fileInput: document.getElementById('file-input'),
    fileInputFiles: document.getElementById('file-input-files'),
    selectFolderBtn: document.getElementById('select-folder-btn'),
    selectFilesBtn: document.getElementById('select-files-btn'),
    
    // Processing elements
    processingPanel: document.getElementById('processing-panel'),
    progressFill: document.getElementById('progress-fill'),
    progressText: document.getElementById('progress-text'),
    processedCount: document.getElementById('processed-count'),
    totalCount: document.getElementById('total-count'),
    currentFile: document.getElementById('current-file'),
    currentStatus: document.getElementById('current-status'),
    
    // Data table elements
    dataTable: document.getElementById('data-table'),
    dataTbody: document.getElementById('data-tbody'),
    selectAllCheckbox: document.getElementById('select-all'),
    bulkActions: document.getElementById('bulk-actions'),
    bulkApproveBtn: document.getElementById('bulk-approve-btn'),
    bulkRejectBtn: document.getElementById('bulk-reject-btn'),
    selectedCount: document.getElementById('selected-count'),
    
    // Filters
    typeFilter: document.getElementById('type-filter'),
    searchInput: document.getElementById('search-input'),
    
    // Header elements
    pageTitle: document.getElementById('page-title'),
    pageSubtitle: document.getElementById('page-subtitle'),
    refreshAll: document.getElementById('refresh-all'),
    quickExport: document.getElementById('quick-export'),
    
    // Statistics elements
    totalFiles: document.getElementById('total-files'),
    processingFiles: document.getElementById('processing-files'),
    completedFiles: document.getElementById('completed-files'),
    totalRecords: document.getElementById('total-records'),
    pendingRecords: document.getElementById('pending-records'),
    approvedRecords: document.getElementById('approved-records'),
    totalAmount: document.getElementById('total-amount'),
    pendingBadge: document.getElementById('pending-badge'),
    approvedBadge: document.getElementById('approved-badge'),
    
    // Modal elements
    editModal: document.getElementById('edit-modal'),
    editForm: document.getElementById('edit-form'),
    modalClose: document.getElementById('modal-close'),
    modalCancel: document.getElementById('modal-cancel'),
    modalSave: document.getElementById('modal-save'),
    
    // Connection status
    connectionStatus: document.getElementById('connection-status'),
    statusDot: document.getElementById('status-dot'),
    statusText: document.getElementById('status-text'),
    lastUpdate: document.getElementById('last-update'),
    
    // Activity timeline
    activityTimeline: document.getElementById('activity-timeline'),
    
    // Toast container
    toastContainer: document.getElementById('toast-container')
};

// Initialize Application
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing application...');
    initializeWebSocket();
    setupEventListeners();
    setupNavigation();
    loadPendingData();
    updateStatistics();
    updateActivityTimeline();
});

// Navigation System
function setupNavigation() {
    elements.navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const tab = link.getAttribute('data-tab');
            if (tab) {
                switchTab(tab);
            }
        });
    });
    
    // Quick action handlers
    document.querySelectorAll('.quick-action').forEach(action => {
        action.addEventListener('click', (e) => {
            const actionType = action.getAttribute('data-action');
            handleQuickAction(actionType);
        });
    });
    
    // Sidebar toggle
    if (elements.sidebarToggle) {
        elements.sidebarToggle.addEventListener('click', () => {
            elements.sidebar.classList.toggle('collapsed');
        });
    }
}

function switchTab(tabName) {
    // Update navigation
    elements.navLinks.forEach(link => {
        link.parentElement.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeLink) {
        activeLink.parentElement.classList.add('active');
    }
    
    // Update content
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    const activeTab = document.getElementById(`${tabName}-tab`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    // Update page title
    const titles = {
        dashboard: { title: 'Επισκόπηση Συστήματος', subtitle: 'Παρακολούθηση και διαχείριση εξαγωγής δεδομένων' },
        upload: { title: 'Φόρτωση Αρχείων', subtitle: 'Ανέβασμα και επεξεργασία νέων αρχείων' },
        queue: { title: 'Ουρά Επεξεργασίας', subtitle: 'Διαχείριση και έγκριση εξαχθέντων δεδομένων' },
        approved: { title: 'Εγκεκριμένα Δεδομένα', subtitle: 'Προβολή και εξαγωγή εγκεκριμένων εγγραφών' },
        analytics: { title: 'Αναλυτικά Στοιχεία', subtitle: 'Στατιστικά και αναφορές συστήματος' },
        exports: { title: 'Διαχείριση Εξαγωγών', subtitle: 'Ιστορικό και διαχείριση εξαγωγών δεδομένων' }
    };
    
    if (elements.pageTitle && titles[tabName]) {
        elements.pageTitle.textContent = titles[tabName].title;
        elements.pageSubtitle.textContent = titles[tabName].subtitle;
    }
    
    currentTab = tabName;
    
    // Load data for specific tabs
    if (tabName === 'queue') {
        loadPendingData();
    }
}

function handleQuickAction(actionType) {
    switch (actionType) {
        case 'upload':
            switchTab('upload');
            break;
        case 'approve-all':
            if (selectedRecords.size > 0) {
                bulkUpdateRecords('approved');
            } else {
                showToast('Δεν έχετε επιλέξει εγγραφές', 'warning');
            }
            break;
        case 'export':
            exportToExcel();
            break;
        case 'analytics':
            switchTab('analytics');
            break;
    }
}

// WebSocket Functions
function initializeWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    socket = new WebSocket(wsUrl);
    
    socket.onopen = function(event) {
        updateConnectionStatus('connected', 'Συνδεδεμένο');
        showToast('Η σύνδεση με τον διακομιστή επιτεύχθηκε', 'success');
    };
    
    socket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
        } catch (e) {
            console.log('WebSocket message:', event.data);
        }
    };
    
    socket.onclose = function(event) {
        updateConnectionStatus('disconnected', 'Αποσυνδεδεμένο');
        showToast('Η σύνδεση με τον διακομιστή διακόπηκε', 'warning');
        
        setTimeout(() => {
            updateConnectionStatus('connecting', 'Επανασύνδεση...');
            initializeWebSocket();
        }, 3000);
    };
    
    socket.onerror = function(error) {
        updateConnectionStatus('disconnected', 'Σφάλμα σύνδεσης');
        showToast('Σφάλμα σύνδεσης με τον διακομιστή', 'error');
    };
}

function handleWebSocketMessage(data) {
    switch (data.type) {
        case 'upload_started':
            showProcessingPanel();
            updateProgress(0, data.message);
            updateProcessingStats(0, data.total_files);
            addActivityItem('Ξεκίνησε η φόρτωση αρχείων', 'info');
            break;
            
        case 'upload_progress':
        case 'processing_progress':
            updateProgress(data.progress, data.message);
            if (data.processed && data.total) {
                updateProcessingStats(data.processed, data.total);
            }
            break;
            
        case 'file_processed':
            showToast(`Επεξεργάστηκε: ${data.data.filename}`, 'success');
            addActivityItem(`Επεξεργάστηκε αρχείο: ${data.data.filename}`, 'success');
            loadPendingData();
            updateStatistics();
            break;
            
        case 'file_duplicate':
            showToast(`Διπλότυπο αρχείο: ${data.data.filename}`, 'warning');
            addActivityItem(`Παραλείφθηκε διπλότυπο: ${data.data.filename}`, 'warning');
            break;
            
        case 'processing_completed':
            updateProgress(100, data.message);
            showToast(data.message, 'success');
            addActivityItem('Ολοκληρώθηκε η επεξεργασία', 'success');
            
            setTimeout(() => {
                hideProcessingPanel();
                loadPendingData();
                updateStatistics();
            }, 2000);
            break;
            
        case 'record_updated':
            showToast(data.message, 'success');
            addActivityItem(data.message, 'success');
            loadPendingData();
            updateStatistics();
            break;
            
        case 'bulk_update':
            showToast(data.message, 'success');
            addActivityItem(`Μαζική ενημέρωση: ${data.count} εγγραφές`, 'success');
            loadPendingData();
            updateStatistics();
            break;
            
        case 'record_deleted':
            showToast(data.message, 'info');
            addActivityItem('Διαγραφή εγγραφής', 'info');
            loadPendingData();
            updateStatistics();
            break;
            
        case 'export_completed':
            showToast(data.message, 'success');
            addActivityItem('Ολοκληρώθηκε η εξαγωγή', 'success');
            break;
            
        case 'error':
            showToast(data.message, 'error');
            addActivityItem('Σφάλμα συστήματος', 'error');
            hideProcessingPanel();
            break;
    }
}

// Event Listeners
function setupEventListeners() {
    // Upload functionality - check if elements exist
    const selectFolderBtn = document.getElementById('select-folder-btn');
    const selectFilesBtn = document.getElementById('select-files-btn');
    const fileInput = document.getElementById('file-input');
    const fileInputFiles = document.getElementById('file-input-files');
    const uploadArea = document.getElementById('upload-area');
    
    if (selectFolderBtn && fileInput) {
        selectFolderBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }
    
    if (selectFilesBtn && fileInputFiles) {
        selectFilesBtn.addEventListener('click', () => {
            fileInputFiles.click();
        });
    }
    
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelection);
    }
    
    if (fileInputFiles) {
        fileInputFiles.addEventListener('change', handleFileSelection);
    }
    
    // Drag and drop
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
    }
    
    // Control buttons
    const refreshAll = document.getElementById('refresh-all');
    const quickExport = document.getElementById('quick-export');
    const bulkApproveBtn = document.getElementById('bulk-approve-btn');
    const bulkRejectBtn = document.getElementById('bulk-reject-btn');
    const selectAllCheckbox = document.getElementById('select-all');
    const typeFilter = document.getElementById('type-filter');
    const searchInput = document.getElementById('search-input');
    
    if (refreshAll) {
        refreshAll.addEventListener('click', () => {
            loadPendingData();
            updateStatistics();
        });
    }
    
    if (quickExport) {
        quickExport.addEventListener('click', exportToExcel);
    }
    
    if (bulkApproveBtn) {
        bulkApproveBtn.addEventListener('click', () => bulkUpdateRecords('approved'));
    }
    
    if (bulkRejectBtn) {
        bulkRejectBtn.addEventListener('click', () => bulkUpdateRecords('rejected'));
    }
    
    // Select all checkbox
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', handleSelectAll);
    }
    
    // Filters
    if (typeFilter) {
        typeFilter.addEventListener('change', filterData);
    }
    
    if (searchInput) {
        searchInput.addEventListener('input', filterData);
    }
    
    // Modal events
    const modalClose = document.getElementById('modal-close');
    const modalCancel = document.getElementById('modal-cancel');
    const modalSave = document.getElementById('modal-save');
    const editModal = document.getElementById('edit-modal');
    
    if (modalClose) {
        modalClose.addEventListener('click', closeModal);
    }
    
    if (modalCancel) {
        modalCancel.addEventListener('click', closeModal);
    }
    
    if (modalSave) {
        modalSave.addEventListener('click', saveEditedRecord);
    }
    
    // Close modal on outside click
    if (editModal) {
        editModal.addEventListener('click', (e) => {
            if (e.target === editModal) {
                closeModal();
            }
        });
    }
}

// Progress and Processing Functions
function showProcessingPanel() {
    const processingPanel = document.getElementById('processing-panel');
    if (processingPanel) {
        processingPanel.style.display = 'block';
        switchTab('upload'); // Switch to upload tab to show progress
    }
}

function hideProcessingPanel() {
    const processingPanel = document.getElementById('processing-panel');
    if (processingPanel) {
        processingPanel.style.display = 'none';
    }
}

function updateProgress(percentage, message) {
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const currentStatus = document.getElementById('current-status');
    
    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }
    if (progressText) {
        progressText.textContent = `${percentage}%`;
    }
    if (currentStatus) {
        currentStatus.textContent = message || 'Επεξεργασία...';
    }
}

function updateProcessingStats(processed, total) {
    const processedCount = document.getElementById('processed-count');
    const totalCount = document.getElementById('total-count');
    
    if (processedCount) {
        processedCount.textContent = processed;
    }
    if (totalCount) {
        totalCount.textContent = total;
    }
}

// Activity Timeline
function addActivityItem(message, type = 'info') {
    const activityTimeline = document.getElementById('activity-timeline');
    if (!activityTimeline) return;
    
    const iconMap = {
        success: 'fas fa-check',
        error: 'fas fa-exclamation-triangle',
        warning: 'fas fa-exclamation-circle',
        info: 'fas fa-info-circle'
    };
    
    const activityItem = document.createElement('div');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-icon ${type}">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-title">${message}</div>
            <div class="activity-time">${new Date().toLocaleTimeString('el-GR')}</div>
        </div>
    `;
    
    activityTimeline.insertBefore(activityItem, activityTimeline.firstChild);
    
    // Keep only last 10 items
    while (activityTimeline.children.length > 10) {
        activityTimeline.removeChild(activityTimeline.lastChild);
    }
}

function updateActivityTimeline() {
    const activityTimeline = document.getElementById('activity-timeline');
    if (activityTimeline) {
        activityTimeline.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon success">
                    <i class="fas fa-check"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">Σύστημα έτοιμο προς χρήση</div>
                    <div class="activity-time">${new Date().toLocaleTimeString('el-GR')}</div>
                </div>
            </div>
        `;
    }
}

// Statistics Functions
async function updateStatistics() {
    try {
        const response = await fetch('/statistics');
        const result = await response.json();
        
        if (result.success) {
            const stats = result.data;
            
            // Update dashboard metrics - safely
            const totalRecords = document.getElementById('total-records');
            const pendingRecords = document.getElementById('pending-records');
            const approvedRecords = document.getElementById('approved-records');
            const totalAmount = document.getElementById('total-amount');
            const totalFiles = document.getElementById('total-files');
            const processingFiles = document.getElementById('processing-files');
            const completedFiles = document.getElementById('completed-files');
            const pendingBadge = document.getElementById('pending-badge');
            const approvedBadge = document.getElementById('approved-badge');
            const lastUpdate = document.getElementById('last-update');
            
            if (totalRecords) totalRecords.textContent = stats.total_records || 0;
            if (pendingRecords) pendingRecords.textContent = stats.pending || 0;
            if (approvedRecords) approvedRecords.textContent = stats.approved || 0;
            if (totalAmount) totalAmount.textContent = `€${(stats.total_amount || 0).toFixed(2)}`;
            if (totalFiles) totalFiles.textContent = stats.total_records || 0;
            if (processingFiles) processingFiles.textContent = stats.pending || 0;
            if (completedFiles) completedFiles.textContent = stats.approved || 0;
            if (pendingBadge) pendingBadge.textContent = stats.pending || 0;
            if (approvedBadge) approvedBadge.textContent = stats.approved || 0;
            if (lastUpdate) lastUpdate.textContent = `${new Date().toLocaleTimeString('el-GR')}`;
        }
    } catch (error) {
        console.error('Error updating statistics:', error);
    }
}

// Connection Status
function updateConnectionStatus(status, text) {
    const statusDot = document.getElementById('status-dot');
    const statusText = document.getElementById('status-text');
    
    if (statusDot) {
        statusDot.className = `fas fa-circle ${status}`;
    }
    if (statusText) {
        statusText.textContent = text;
    }
}

// File Upload Functions
function handleFileSelection(event) {
    const files = Array.from(event.target.files);
    if (files.length > 0) {
        uploadFiles(files);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.classList.add('dragover');
    }
}

function handleDragLeave(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.classList.remove('dragover');
    }
}

function handleDrop(event) {
    event.preventDefault();
    const uploadArea = document.getElementById('upload-area');
    if (uploadArea) {
        uploadArea.classList.remove('dragover');
    }
    
    const files = Array.from(event.dataTransfer.files);
    if (files.length > 0) {
        uploadFiles(files);
    }
}

async function uploadFiles(files) {
    const formData = new FormData();
    
    const supportedFiles = files.filter(file => {
        const ext = file.name.toLowerCase().split('.').pop();
        return ['html', 'eml'].includes(ext);
    });
    
    if (supportedFiles.length === 0) {
        showToast('Δεν βρέθηκαν υποστηριζόμενα αρχεία', 'warning');
        return;
    }
    
    supportedFiles.forEach(file => {
        formData.append('files', file);
    });
    
    try {
        const response = await fetch('/upload-folder', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (!result.success) {
            showToast(`Σφάλμα: ${result.error}`, 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
        hideProcessingPanel();
    }
}

// Data Loading and Table Functions
async function loadPendingData() {
    try {
        const response = await fetch('/pending-data');
        const result = await response.json();
        
        if (result.success) {
            pendingData = result.data;
            renderDataTable();
            updateSelectionButtons();
        } else {
            showToast('Σφάλμα φόρτωσης δεδομένων', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

function renderDataTable() {
    const dataTbody = document.getElementById('data-tbody');
    if (!dataTbody) return;
    
    if (pendingData.length === 0) {
        dataTbody.innerHTML = `
            <tr class="no-data">
                <td colspan="12">
                    <div class="no-data-content">
                        <i class="fas fa-inbox"></i>
                        <h3>Δεν υπάρχουν δεδομένα</h3>
                        <p>Ανεβάστε αρχεία για να ξεκινήσετε την επεξεργασία</p>
                        <button class="btn-primary" onclick="switchTab('upload')">
                            <i class="fas fa-cloud-upload-alt"></i>
                            Φόρτωση Αρχείων
                        </button>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    const tbody = pendingData.map(record => createTableRow(record)).join('');
    dataTbody.innerHTML = tbody;
    
    // Add event listeners
    attachTableEventListeners();
}

function createTableRow(record) {
    const typeClass = `type-${record.type.toLowerCase()}`;
    const priorityClass = record.priority ? `priority-${record.priority.toLowerCase()}` : '';
    
    return `
        <tr data-id="${record.id}">
            <td class="checkbox-col">
                <label class="checkbox-container">
                    <input type="checkbox" class="record-checkbox" value="${record.id}">
                    <span class="checkmark"></span>
                </label>
            </td>
            <td>
                <span class="type-badge ${typeClass}">${translateType(record.type)}</span>
            </td>
            <td title="${record.source_file || ''}">${truncateText(record.source_file, 20)}</td>
            <td>${record.client_name || '-'}</td>
            <td>${record.company || '-'}</td>
            <td>${record.email || '-'}</td>
            <td>${record.phone || '-'}</td>
            <td>${record.service_interest || '-'}</td>
            <td>${formatAmount(record.total_amount || record.amount)}</td>
            <td>
                ${record.priority ? `<span class="${priorityClass}">${record.priority}</span>` : '-'}
            </td>
            <td>${formatDate(record.date_extracted)}</td>
            <td class="actions-col">
                <div class="actions">
                    <button class="btn-small btn-warning btn-edit" data-id="${record.id}" title="Επεξεργασία">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-success btn-approve" data-id="${record.id}" title="Έγκριση">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn-small btn-danger btn-reject" data-id="${record.id}" title="Απόρριψη">
                        <i class="fas fa-times"></i>
                    </button>
                    <button class="btn-small btn-secondary btn-delete" data-id="${record.id}" title="Διαγραφή">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

function attachTableEventListeners() {
    document.querySelectorAll('.record-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleRecordSelection);
    });
    
    document.querySelectorAll('.btn-edit').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const recordId = e.target.closest('button').getAttribute('data-id');
            if (recordId) openEditModal(recordId);
        });
    });
    
    document.querySelectorAll('.btn-approve').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const recordId = e.target.closest('button').getAttribute('data-id');
            if (recordId) updateSingleRecord(recordId, 'approved');
        });
    });
    
    document.querySelectorAll('.btn-reject').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const recordId = e.target.closest('button').getAttribute('data-id');
            if (recordId) updateSingleRecord(recordId, 'rejected');
        });
    });
    
    document.querySelectorAll('.btn-delete').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const recordId = e.target.closest('button').getAttribute('data-id');
            if (recordId) deleteRecord(recordId);
        });
    });
}

// Record Management Functions
async function updateSingleRecord(recordId, action) {
    if (!recordId || recordId === 'undefined') {
        showToast('Σφάλμα: Μη έγκυρο ID εγγραφής', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('action', action);
    
    try {
        const response = await fetch(`/update-record/${recordId}`, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (!result.success) {
            showToast('Σφάλμα ενημέρωσης εγγραφής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

async function bulkUpdateRecords(action) {
    if (selectedRecords.size === 0) {
        showToast('Δεν έχετε επιλέξει εγγραφές', 'warning');
        return;
    }
    
    const recordIds = Array.from(selectedRecords);
    const formData = new FormData();
    formData.append('record_ids', JSON.stringify(recordIds));
    formData.append('action', action);
    
    try {
        const response = await fetch('/bulk-update', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            selectedRecords.clear();
            if (elements.selectAllCheckbox) elements.selectAllCheckbox.checked = false;
            updateSelectionButtons();
        } else {
            showToast('Σφάλμα μαζικής ενημέρωσης', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

async function deleteRecord(recordId) {
    if (!recordId || recordId === 'undefined') {
        showToast('Σφάλμα: Μη έγκυρο ID εγγραφής', 'error');
        return;
    }
    
    if (!confirm('Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εγγραφή;')) {
        return;
    }
    
    try {
        const response = await fetch(`/delete-record/${recordId}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (!result.success) {
            showToast('Σφάλμα διαγραφής εγγραφής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

// Selection Functions
function handleSelectAll() {
    if (!elements.selectAllCheckbox) return;
    
    const isChecked = elements.selectAllCheckbox.checked;
    const checkboxes = document.querySelectorAll('.record-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        if (isChecked) {
            selectedRecords.add(parseInt(checkbox.value));
        } else {
            selectedRecords.delete(parseInt(checkbox.value));
        }
    });
    
    updateSelectionButtons();
}

function handleRecordSelection(event) {
    const recordId = parseInt(event.target.value);
    
    if (event.target.checked) {
        selectedRecords.add(recordId);
    } else {
        selectedRecords.delete(recordId);
        if (elements.selectAllCheckbox) elements.selectAllCheckbox.checked = false;
    }
    
    updateSelectionButtons();
}

function updateSelectionButtons() {
    const hasSelection = selectedRecords.size > 0;
    
    if (elements.bulkActions) {
        elements.bulkActions.style.display = hasSelection ? 'flex' : 'none';
    }
    
    if (elements.selectedCount) {
        elements.selectedCount.textContent = `${selectedRecords.size} επιλεγμένα`;
    }
    
    if (elements.bulkRejectBtn) elements.bulkRejectBtn.disabled = !hasSelection;
}

// Filter Functions
function filterData() {
    const typeFilter = elements.typeFilter?.value || '';
    const searchTerm = elements.searchInput?.value.toLowerCase() || '';
    
    let filteredData = pendingData;
    
    if (typeFilter) {
        filteredData = filteredData.filter(record => record.type === typeFilter);
    }
    
    if (searchTerm) {
        filteredData = filteredData.filter(record => {
            const searchableText = [
                record.client_name,
                record.company,
                record.email,
                record.service_interest,
                record.source_file
            ].join(' ').toLowerCase();
            
            return searchableText.includes(searchTerm);
        });
    }
    
    renderFilteredTable(filteredData);
}

function renderFilteredTable(data) {
    if (!elements.dataTbody) return;
    
    if (data.length === 0) {
        elements.dataTbody.innerHTML = `
            <tr class="no-data">
                <td colspan="12">
                    <div class="no-data-content">
                        <i class="fas fa-search"></i>
                        <h3>Δεν βρέθηκαν αποτελέσματα</h3>
                        <p>Δοκιμάστε διαφορετικά κριτήρια αναζήτησης</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    const tbody = data.map(record => createTableRow(record)).join('');
    elements.dataTbody.innerHTML = tbody;
    
    attachTableEventListeners();
}

// Modal Functions
function openEditModal(recordId) {
    const record = pendingData.find(r => r.id == recordId);
    if (!record) return;
    
    // Populate form fields
    document.getElementById('edit-record-id').value = record.id;
    document.getElementById('edit-client-name').value = record.client_name || '';
    document.getElementById('edit-email').value = record.email || '';
    document.getElementById('edit-phone').value = record.phone || '';
    document.getElementById('edit-company').value = record.company || '';
    document.getElementById('edit-service').value = record.service_interest || '';
    document.getElementById('edit-amount').value = record.total_amount || record.amount || '';
    document.getElementById('edit-priority').value = record.priority || '';
    document.getElementById('edit-message').value = record.message || '';
    
    if (elements.editModal) {
        elements.editModal.classList.add('active');
    }
}

function closeModal() {
    if (elements.editModal) {
        elements.editModal.classList.remove('active');
    }
    if (elements.editForm) {
        elements.editForm.reset();
    }
}

async function saveEditedRecord() {
    const recordId = document.getElementById('edit-record-id').value;
    const formData = new FormData(elements.editForm);
    formData.append('action', 'approved');
    
    const updatedData = {};
    formData.forEach((value, key) => {
        if (key !== 'action' && value.trim() !== '') {
            updatedData[key] = value.trim();
        }
    });
    
    const submitFormData = new FormData();
    submitFormData.append('action', 'approved');
    submitFormData.append('updated_data', JSON.stringify(updatedData));
    
    try {
        const response = await fetch(`/update-record/${recordId}`, {
            method: 'POST',
            body: submitFormData
        });
        
        const result = await response.json();
        
        if (result.success) {
            closeModal();
        } else {
            showToast('Σφάλμα αποθήκευσης εγγραφής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

// Export Functions
async function exportToExcel() {
    const formData = new FormData();
    formData.append('format_type', 'greek');
    
    try {
        const response = await fetch('/export-excel', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `extracted_data_${new Date().toISOString().slice(0, 10)}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showToast('Η εξαγωγή ολοκληρώθηκε επιτυχώς', 'success');
        } else {
            const result = await response.json();
            showToast(result.detail || 'Σφάλμα εξαγωγής', 'error');
        }
    } catch (error) {
        showToast(`Σφάλμα δικτύου: ${error.message}`, 'error');
    }
}

// Toast Notification System
function showToast(message, type = 'info') {
    if (!elements.toastContainer) {
        console.warn('Toast container not found');
        return;
    }
    
    const toastId = 'toast_' + Date.now();
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.id = toastId;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="toast-content">
            <div class="toast-message">${message}</div>
        </div>
        <button class="toast-close" onclick="removeToast('${toastId}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    elements.toastContainer.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => removeToast(toastId), 5000);
}

function removeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.style.animation = 'toastSlideOut 0.3s ease forwards';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

// Utility Functions
function translateType(type) {
    const translations = {
        'FORM': 'Φόρμα',
        'EMAIL': 'Email',
        'INVOICE': 'Τιμολόγιο'
    };
    return translations[type] || type;
}

function formatAmount(amount) {
    if (!amount) return '-';
    return `€${parseFloat(amount).toFixed(2)}`;
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR');
}

function truncateText(text, maxLength) {
    if (!text) return '-';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// Global functions for onclick handlers
window.switchTab = switchTab;
window.removeToast = removeToast;

// Add CSS for toast slide out animation
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);