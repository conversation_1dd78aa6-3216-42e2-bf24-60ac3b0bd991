/* Modern Enterprise CSS Variables */
:root {
    /* Color Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;
    
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    
    --warning-50: #fefce8;
    --warning-500: #eab308;
    --warning-600: #ca8a04;
    --warning-700: #a16207;
    
    --danger-50: #fef2f2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-700: #b91c1c;
    
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    
    /* Typography */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Borders */
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 80px;
}

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    font-size: var(--text-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
    position: relative;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    min-width: var(--sidebar-width);
    background: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    left: 0;
    top: 0;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
    min-width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-lg);
    flex-shrink: 0;
}

.logo-text {
    font-weight: 700;
    font-size: var(--text-xl);
    color: var(--gray-900);
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--border-radius);
    color: var(--gray-500);
    cursor: pointer;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: var(--space-6);
    overflow-y: auto;
}

.nav-section {
    margin-bottom: var(--space-8);
}

.nav-title {
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--gray-500);
    margin-bottom: var(--space-4);
    padding-left: var(--space-3);
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-title {
    opacity: 0;
    display: none;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: var(--space-1);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    border-radius: var(--border-radius);
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

.nav-link.active,
.nav-item.active .nav-link {
    background: var(--primary-50);
    color: var(--primary-700);
}

.nav-icon {
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    display: none;
}

.nav-badge {
    background: var(--gray-400);
    color: white;
    font-size: var(--text-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    transition: opacity 0.3s ease;
}

.nav-badge.success {
    background: var(--success-500);
}

.sidebar.collapsed .nav-badge {
    opacity: 0;
    display: none;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

.connection-status {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    font-weight: 500;
}

#status-dot {
    font-size: 8px;
}

#status-dot.connected {
    color: var(--success-500);
}

#status-dot.disconnected {
    color: var(--danger-500);
}

#status-dot.connecting {
    color: var(--warning-500);
}

.status-details {
    font-size: var(--text-xs);
    color: var(--gray-500);
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .status-details {
    opacity: 0;
    display: none;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: calc(100% - var(--sidebar-width));
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
}

/* Top Header */
.top-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6) var(--space-8);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--header-height);
    flex-shrink: 0;
}

.header-left {
    flex: 1;
}

.page-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.page-subtitle {
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.header-stats {
    display: flex;
    gap: var(--space-6);
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
}

.stat-value.processing {
    color: var(--warning-600);
}

.stat-value.success {
    color: var(--success-600);
}

.stat-label {
    display: block;
    font-size: var(--text-xs);
    color: var(--gray-500);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: var(--space-1);
}

.header-actions {
    display: flex;
    gap: var(--space-3);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--text-sm);
}

.action-btn.primary {
    background: var(--primary-600);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
}

.action-btn.secondary:hover {
    background: var(--gray-200);
}

/* Content Wrapper */
.content-wrapper {
    flex: 1;
    padding: var(--space-8);
    overflow-y: auto;
    min-height: 0;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Dashboard */
.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
}

.metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
}

.metric-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
}

.metric-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.metric-card.primary {
    border-left: 4px solid var(--primary-500);
}

.metric-card.success {
    border-left: 4px solid var(--success-500);
}

.metric-card.warning {
    border-left: 4px solid var(--warning-500);
}

.metric-card.info {
    border-left: 4px solid var(--primary-500);
}

.metric-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.metric-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
}

.metric-card.primary .metric-icon {
    background: var(--primary-50);
    color: var(--primary-600);
}

.metric-card.success .metric-icon {
    background: var(--success-50);
    color: var(--success-600);
}

.metric-card.warning .metric-icon {
    background: var(--warning-50);
    color: var(--warning-600);
}

.metric-card.info .metric-icon {
    background: var(--primary-50);
    color: var(--primary-600);
}

.metric-header h3 {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--gray-700);
}

.metric-value {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.metric-change {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.metric-change.positive {
    color: var(--success-600);
}

.dashboard-secondary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.activity-panel,
.quick-actions-panel {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.panel-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header h3 {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.btn-link {
    background: none;
    border: none;
    color: var(--primary-600);
    font-size: var(--text-sm);
    cursor: pointer;
    text-decoration: none;
}

.btn-link:hover {
    text-decoration: underline;
}

.activity-timeline {
    padding: var(--space-6);
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    flex-shrink: 0;
}

.activity-icon.success {
    background: var(--success-50);
    color: var(--success-600);
}

.activity-icon.info {
    background: var(--primary-50);
    color: var(--primary-600);
}

.activity-icon.warning {
    background: var(--warning-50);
    color: var(--warning-600);
}

.activity-icon.error {
    background: var(--danger-50);
    color: var(--danger-600);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
    font-size: var(--text-sm);
}

.activity-time {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.quick-actions-grid {
    padding: var(--space-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-6);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.quick-action:hover {
    background: var(--primary-50);
    border-color: var(--primary-200);
    transform: translateY(-2px);
}

.quick-action i {
    font-size: var(--text-xl);
    color: var(--primary-600);
}

.quick-action span {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-700);
}

/* Upload Section */
.upload-container {
    max-width: 800px;
    margin: 0 auto;
}

.upload-header {
    text-align: center;
    margin-bottom: var(--space-8);
}

.upload-title h2 {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.upload-title p {
    font-size: var(--text-lg);
    color: var(--gray-500);
}

.upload-zone {
    background: white;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-xl);
    padding: var(--space-12);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: var(--primary-500);
    background: var(--primary-50);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-6);
}

.upload-icon i {
    font-size: 4rem;
    color: var(--gray-400);
    transition: color 0.3s ease;
}

.upload-zone:hover .upload-icon i,
.upload-zone.dragover .upload-icon i {
    color: var(--primary-500);
}

.upload-content h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.upload-content p {
    color: var(--gray-500);
    margin-bottom: var(--space-4);
}

.upload-options {
    display: flex;
    gap: var(--space-4);
}

.upload-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-6);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.upload-btn.primary {
    background: var(--primary-600);
    color: white;
}

.upload-btn.primary:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.upload-btn.secondary {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.upload-btn.secondary:hover {
    background: var(--gray-50);
}

/* Processing Panel */
.processing-panel {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--space-6);
    margin-top: var(--space-8);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.processing-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
}

.processing-header h3 {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.processing-stats {
    font-size: var(--text-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.progress-container {
    margin-bottom: var(--space-6);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--text-xs);
    font-weight: 600;
    color: var(--gray-700);
}

.processing-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.detail-label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-600);
}

.detail-value {
    font-size: var(--text-sm);
    color: var(--gray-900);
    font-weight: 500;
}

/* Queue Section */
.queue-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.queue-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--space-6);
}

.queue-title h2 {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.queue-title p {
    color: var(--gray-500);
}

.queue-actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    align-items: flex-end;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-3) var(--space-4);
    background: var(--primary-50);
    border: 1px solid var(--primary-200);
    border-radius: var(--border-radius);
}

.selected-count {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--primary-700);
}

.filter-controls {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

.filter-select {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--text-sm);
    background: white;
    cursor: pointer;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--space-3);
    color: var(--gray-400);
    font-size: var(--text-sm);
}

.search-box input {
    padding: var(--space-2) var(--space-3) var(--space-2) var(--space-8);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--text-sm);
    width: 200px;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-50);
}

/* Data Table */
.data-table-container {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--gray-50);
    padding: var(--space-4);
    text-align: left;
    font-weight: 600;
    font-size: var(--text-sm);
    color: var(--gray-700);
    border-bottom: 1px solid var(--gray-200);
    white-space: nowrap;
}

.data-table td {
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-100);
    font-size: var(--text-sm);
    vertical-align: middle;
}

.data-table tbody tr:hover {
    background: var(--gray-50);
}

.data-table tbody tr.selected {
    background: var(--primary-50);
}

.checkbox-col {
    width: 50px;
}

.actions-col {
    width: 200px;
}

/* Custom Checkbox */
.checkbox-container {
    display: block;
    position: relative;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    display: block;
    height: 18px;
    width: 18px;
    background: white;
    border: 2px solid var(--gray-300);
    border-radius: 3px;
    transition: all 0.2s ease;
}

.checkbox-container:hover .checkmark {
    border-color: var(--primary-500);
}

.checkbox-container input:checked ~ .checkmark {
    background: var(--primary-600);
    border-color: var(--primary-600);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

/* Type Badges */
.type-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.type-badge.type-form {
    background: var(--primary-50);
    color: var(--primary-700);
}

.type-badge.type-email {
    background: var(--warning-50);
    color: var(--warning-700);
}

.type-badge.type-invoice {
    background: var(--success-50);
    color: var(--success-700);
}

/* Priority Badges */
.priority-high {
    background: var(--danger-50);
    color: var(--danger-700);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 600;
}

.priority-medium {
    background: var(--warning-50);
    color: var(--warning-700);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 600;
}

.priority-low {
    background: var(--success-50);
    color: var(--success-700);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--text-xs);
    font-weight: 600;
}

/* No Data State */
.no-data {
    text-align: center;
}

.no-data-content {
    padding: var(--space-12);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
}

.no-data-content i {
    font-size: 3rem;
    color: var(--gray-300);
}

.no-data-content h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-700);
}

.no-data-content p {
    color: var(--gray-500);
    margin-bottom: var(--space-4);
}

/* Action Buttons in Table */
.actions {
    display: flex;
    gap: var(--space-2);
}

.btn-small {
    padding: var(--space-2);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-small:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-small.btn-warning {
    background: var(--warning-500);
    color: white;
}

.btn-small.btn-warning:hover {
    background: var(--warning-600);
}

.btn-small.btn-success {
    background: var(--success-500);
    color: white;
}

.btn-small.btn-success:hover {
    background: var(--success-600);
}

.btn-small.btn-danger {
    background: var(--danger-500);
    color: white;
}

.btn-small.btn-danger:hover {
    background: var(--danger-600);
}

.btn-small.btn-secondary {
    background: var(--gray-500);
    color: white;
}

.btn-small.btn-secondary:hover {
    background: var(--gray-600);
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: var(--space-4);
}

.modal-overlay.active {
    display: flex;
}

.modal-container {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.modal-close {
    background: none;
    border: none;
    padding: var(--space-2);
    border-radius: var(--border-radius);
    cursor: pointer;
    color: var(--gray-400);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--space-6);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
}

/* Form Styles */
.edit-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.form-input,
.form-select,
.form-textarea {
    padding: var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--text-sm);
    transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-50);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Button Styles */
.btn-primary {
    background: var(--primary-600);
    color: white;
    border: none;
    padding: var(--space-3) var(--space-4);
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
}

.btn-primary:hover {
    background: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    max-width: 400px;
}

.toast {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    animation: toastSlideIn 0.3s ease;
    position: relative;
    overflow: hidden;
}

.toast::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.toast.success::before {
    background: var(--success-500);
}

.toast.error::before {
    background: var(--danger-500);
}

.toast.warning::before {
    background: var(--warning-500);
}

.toast.info::before {
    background: var(--primary-500);
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toastSlideOut {
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
}

.toast.success .toast-icon {
    background: var(--success-50);
    color: var(--success-600);
}

.toast.error .toast-icon {
    background: var(--danger-50);
    color: var(--danger-600);
}

.toast.warning .toast-icon {
    background: var(--warning-50);
    color: var(--warning-600);
}

.toast.info .toast-icon {
    background: var(--primary-50);
    color: var(--primary-600);
}

.toast-content {
    flex: 1;
}

.toast-message {
    color: var(--gray-600);
    font-size: var(--text-sm);
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    margin-left: var(--space-2);
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Other Sections Styling */
.approved-container,
.analytics-container,
.exports-container {
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
    padding: var(--space-12);
}

.approved-container h2,
.analytics-container h2,
.exports-container h2 {
    font-size: var(--text-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
}

.approved-container p,
.analytics-container p,
.exports-container p {
    font-size: var(--text-lg);
    color: var(--gray-500);
    margin-bottom: var(--space-8);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .sidebar.collapsed + .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .dashboard-secondary {
        grid-template-columns: 1fr;
    }
    
    .metrics-row {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .header-stats {
        display: none;
    }
    
    .queue-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }
    
    .queue-actions {
        width: 100%;
        align-items: stretch;
    }
    
    .filter-controls {
        flex-wrap: wrap;
        width: 100%;
    }
    
    .search-box input {
        width: 100%;
        min-width: 200px;
    }
    
    .data-table-container {
        overflow-x: auto;
    }
    
    .data-table {
        min-width: 800px;
    }
}

@media (max-width: 768px) {
    .top-header {
        padding: var(--space-4);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
        min-height: auto;
    }
    
    .header-right {
        width: 100%;
        justify-content: space-between;
    }
    
    .content-wrapper {
        padding: var(--space-4);
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-row {
        grid-template-columns: 1fr;
    }
    
    .upload-zone {
        padding: var(--space-8);
    }
    
    .upload-content h3 {
        font-size: var(--text-lg);
    }
    
    .page-title {
        font-size: var(--text-xl);
    }
    
    .metric-value {
        font-size: var(--text-2xl);
    }
    
    .actions {
        flex-wrap: wrap;
    }
    
    .btn-small {
        width: 28px;
        height: 28px;
    }
    
    .toast-container {
        left: var(--space-4);
        right: var(--space-4);
        max-width: none;
    }
    
    .processing-details {
        grid-template-columns: 1fr;
    }
}

/* Focus States for Accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
a:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

::-webkit-scrollbar-corner {
    background: var(--gray-100);
}

/* Scrollbar for Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--gray-300) var(--gray-100);
}

/* Selection */
::selection {
    background: var(--primary-100);
    color: var(--primary-900);
}

::-moz-selection {
    background: var(--primary-100);
    color: var(--primary-900);
}