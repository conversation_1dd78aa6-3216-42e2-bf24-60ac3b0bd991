from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, UploadFile, File, Form, Depends, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from sqlalchemy.orm import Session
import json
import asyncio
import os
import shutil
import tempfile
import zipfile
from typing import List, Dict, Optional
from datetime import datetime

from database import create_tables, get_db, save_extracted_data, get_all_pending_data, update_record_status, get_approved_data, delete_record, check_duplicate_file
from extractors import DataExtractors
from export_manager import ExportManager

app = FastAPI(title="Automation Data Extraction System")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create database tables
create_tables()

# Initialize services
data_extractors = DataExtractors()
export_manager = ExportManager()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Remove broken connections
                self.active_connections.remove(connection)

manager = ConnectionManager()

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the main application page"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo back for keepalive
            await manager.send_personal_message(f"Received: {data}", websocket)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.post("/upload-folder")
async def upload_folder(files: List[UploadFile] = File(...), db: Session = Depends(get_db)):
    """Upload and process multiple files as a folder"""
    
    try:
        # Create temporary directory for uploaded files
        temp_dir = tempfile.mkdtemp()
        uploaded_files = []
        
        # Send initial status
        await manager.broadcast(json.dumps({
            "type": "upload_started",
            "message": f"Ξεκίνησε η επεξεργασία {len(files)} αρχείων...",
            "total_files": len(files)
        }))
        
        # Save uploaded files
        for i, file in enumerate(files):
            if file.filename:
                file_path = os.path.join(temp_dir, file.filename)
                
                # Create subdirectories if needed
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)
                
                uploaded_files.append(file_path)
                
                # Send progress update
                await manager.broadcast(json.dumps({
                    "type": "upload_progress",
                    "message": f"Αποθηκεύτηκε αρχείο: {file.filename}",
                    "progress": int((i + 1) / len(files) * 50)  # 50% for upload
                }))
        
        # Process files
        await manager.broadcast(json.dumps({
            "type": "processing_started",
            "message": "Ξεκίνησε η εξαγωγή δεδομένων...",
            "progress": 50
        }))
        
        extraction_results = []
        processed_count = 0
        duplicates_found = 0
        
        for file_path in uploaded_files:
            try:
                print(f"Processing file: {file_path}")  # Debug log
                
                # Check for duplicates first
                duplicate_check = check_duplicate_file(db, file_path)
                
                if duplicate_check["is_duplicate"]:
                    duplicates_found += 1
                    existing = duplicate_check["existing_record"]
                    
                    print(f"Duplicate found: {file_path} - {duplicate_check['duplicate_type']}")
                    
                    extraction_results.append({
                        "filename": os.path.basename(file_path),
                        "status": "duplicate",
                        "duplicate_type": duplicate_check["duplicate_type"],
                        "existing_record": existing,
                        "message": f"Το αρχείο υπάρχει ήδη ({duplicate_check['duplicate_type']})"
                    })
                    
                    # Send real-time update for duplicate
                    await manager.broadcast(json.dumps({
                        "type": "file_duplicate",
                        "message": f"Διπλότυπο: {os.path.basename(file_path)}",
                        "data": {
                            "filename": os.path.basename(file_path),
                            "duplicate_type": duplicate_check["duplicate_type"],
                            "existing_id": existing["id"],
                            "existing_status": existing["status"]
                        }
                    }))
                    
                    processed_count += 1
                    progress = 50 + int((processed_count / len(uploaded_files)) * 50)
                    
                    await manager.broadcast(json.dumps({
                        "type": "processing_progress",
                        "progress": progress,
                        "processed": processed_count,
                        "total": len(uploaded_files)
                    }))
                    
                    continue
                
                # Extract data from file
                extracted_data = data_extractors.extract_from_file(file_path)
                print(f"Extracted data: {extracted_data}")  # Debug log
                
                if 'error' not in extracted_data:
                    # Ensure we have the required fields for database
                    if 'type' not in extracted_data:
                        print(f"Warning: No type field in extracted data for {file_path}")
                        extracted_data['type'] = 'UNKNOWN'
                    
                    # Add file hash to extracted data
                    if duplicate_check.get("file_hash"):
                        extracted_data['file_hash'] = duplicate_check["file_hash"]
                    
                    # Save to database
                    extracted_data['raw_data'] = json.dumps(extracted_data, ensure_ascii=False)
                    print(f"Saving to database: {extracted_data}")  # Debug log
                    
                    try:
                        db_record = save_extracted_data(db, extracted_data)
                        print(f"Saved record with ID: {db_record.id}")  # Debug log
                        
                        extraction_results.append({
                            "id": db_record.id,
                            "filename": os.path.basename(file_path),
                            "type": extracted_data.get('type', 'UNKNOWN'),
                            "status": "success"
                        })
                        
                        # Send real-time update
                        await manager.broadcast(json.dumps({
                            "type": "file_processed",
                            "message": f"Επεξεργάστηκε: {os.path.basename(file_path)}",
                            "data": {
                                "id": db_record.id,
                                "filename": os.path.basename(file_path),
                                "type": extracted_data.get('type'),
                                "client_name": extracted_data.get('client_name'),
                                "company": extracted_data.get('company'),
                                "amount": extracted_data.get('total_amount') or extracted_data.get('amount')
                            }
                        }))
                        
                    except Exception as db_error:
                        print(f"Database error for {file_path}: {str(db_error)}")
                        extraction_results.append({
                            "filename": os.path.basename(file_path),
                            "status": "error",
                            "error": f"Database error: {str(db_error)}"
                        })
                    
                else:
                    print(f"Extraction error for {file_path}: {extracted_data['error']}")
                    extraction_results.append({
                        "filename": os.path.basename(file_path),
                        "status": "error",
                        "error": extracted_data['error']
                    })
                
                processed_count += 1
                progress = 50 + int((processed_count / len(uploaded_files)) * 50)
                
                await manager.broadcast(json.dumps({
                    "type": "processing_progress",
                    "progress": progress,
                    "processed": processed_count,
                    "total": len(uploaded_files)
                }))
                
            except Exception as e:
                print(f"General error processing {file_path}: {str(e)}")
                extraction_results.append({
                    "filename": os.path.basename(file_path),
                    "status": "error",
                    "error": str(e)
                })
                processed_count += 1
        
        # Cleanup temporary directory
        shutil.rmtree(temp_dir)
        
        # Send completion message
        successful_extractions = [r for r in extraction_results if r.get("status") == "success"]
        duplicate_files = [r for r in extraction_results if r.get("status") == "duplicate"]
        error_files = [r for r in extraction_results if r.get("status") == "error"]
        
        completion_message = f"Ολοκληρώθηκε! Επεξεργάστηκαν {len(successful_extractions)} αρχεία"
        if duplicate_files:
            completion_message += f", {len(duplicate_files)} διπλότυπα παραλείφθηκαν"
        if error_files:
            completion_message += f", {len(error_files)} σφάλματα"
        
        await manager.broadcast(json.dumps({
            "type": "processing_completed",
            "message": completion_message,
            "progress": 100,
            "results": extraction_results,
            "summary": {
                "processed": len(successful_extractions),
                "duplicates": len(duplicate_files),
                "errors": len(error_files),
                "total": len(files)
            }
        }))
        
        return {
            "success": True,
            "message": completion_message,
            "results": extraction_results,
            "summary": {
                "processed": len(successful_extractions),
                "duplicates": len(duplicate_files),
                "errors": len(error_files)
            }
        }
        
    except Exception as e:
        await manager.broadcast(json.dumps({
            "type": "error",
            "message": f"Σφάλμα επεξεργασίας: {str(e)}"
        }))
        
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/pending-data")
async def get_pending_data(db: Session = Depends(get_db)):
    """Get all pending data for review"""
    try:
        pending_records = get_all_pending_data(db)
        
        results = []
        for record in pending_records:
            record_dict = {
                "id": record.id,
                "type": record.type,
                "source_file": record.source_file,
                "client_name": record.client_name,
                "email": record.email,
                "phone": record.phone,
                "company": record.company,
                "service_interest": record.service_interest,
                "amount": record.amount,
                "vat": record.vat,
                "total_amount": record.total_amount,
                "invoice_number": record.invoice_number,
                "priority": record.priority,
                "message": record.message,
                "email_type": record.email_type,
                "date_extracted": record.date_extracted.isoformat() if record.date_extracted else None
            }
            results.append(record_dict)
        
        return {
            "success": True,
            "data": results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/update-record/{record_id}")
async def update_record(
    record_id: int,
    action: str = Form(...),  # approve, reject, edit
    updated_data: str = Form(None),  # JSON string of updated data
    db: Session = Depends(get_db)
):
    """Update record status and optionally modify data"""
    try:
        update_dict = {}
        
        if updated_data:
            try:
                update_dict = json.loads(updated_data)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Μη έγκυρα δεδομένα JSON")
        
        # Update record
        updated_record = update_record_status(db, record_id, action, update_dict)
        
        if not updated_record:
            raise HTTPException(status_code=404, detail="Δεν βρέθηκε η εγγραφή")
        
        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "record_updated",
            "message": f"Η εγγραφή {record_id} {'εγκρίθηκε' if action == 'approved' else 'απορρίφθηκε' if action == 'rejected' else 'ενημερώθηκε'}",
            "record_id": record_id,
            "action": action
        }))
        
        return {
            "success": True,
            "message": f"Η εγγραφή ενημερώθηκε επιτυχώς"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/bulk-update")
async def bulk_update_records(
    record_ids: str = Form(...),
    action: str = Form(...),
    db: Session = Depends(get_db)
):
    """Update multiple records at once"""
    try:
        ids = json.loads(record_ids)
        
        updated_count = 0
        for record_id in ids:
            result = update_record_status(db, record_id, action)
            if result:
                updated_count += 1
        
        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "bulk_update",
            "message": f"Ενημερώθηκαν {updated_count} εγγραφές",
            "count": updated_count,
            "action": action
        }))
        
        return {
            "success": True,
            "message": f"Ενημερώθηκαν {updated_count} εγγραφές"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/delete-record/{record_id}")
async def delete_record_endpoint(record_id: int, db: Session = Depends(get_db)):
    """Delete a record"""
    try:
        success = delete_record(db, record_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Δεν βρέθηκε η εγγραφή")
        
        # Send real-time update
        await manager.broadcast(json.dumps({
            "type": "record_deleted",
            "message": f"Διαγράφηκε η εγγραφή {record_id}",
            "record_id": record_id
        }))
        
        return {
            "success": True,
            "message": "Η εγγραφή διαγράφηκε επιτυχώς"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/export-excel")
async def export_to_excel(
    format_type: str = Form("template"),  # template or greek
    db: Session = Depends(get_db)
):
    """Export approved data to Excel"""
    try:
        approved_records = get_approved_data(db)
        
        if not approved_records:
            raise HTTPException(status_code=400, detail="Δεν υπάρχουν εγκεκριμένες εγγραφές για εξαγωγή")
        
        template_format = format_type == "template"
        filepath = export_manager.export_to_excel(approved_records, template_format)
        
        # Send notification
        await manager.broadcast(json.dumps({
            "type": "export_completed",
            "message": f"Η εξαγωγή ολοκληρώθηκε. Εξήχθησαν {len(approved_records)} εγγραφές.",
            "filename": os.path.basename(filepath)
        }))
        
        return FileResponse(
            filepath,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=os.path.basename(filepath)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/export-summary")
async def export_summary_report(db: Session = Depends(get_db)):
    """Export summary report"""
    try:
        from database import ExtractedData
        all_records = db.query(ExtractedData).all()
        
        filepath = export_manager.export_summary_report(all_records)
        
        return FileResponse(
            filepath,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            filename=os.path.basename(filepath)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/debug/all-data")
async def get_all_data_debug(db: Session = Depends(get_db)):
    """Debug endpoint to see all data in database"""
    try:
        from database import ExtractedData
        all_records = db.query(ExtractedData).all()
        
        results = []
        for record in all_records:
            record_dict = {
                "id": record.id,
                "type": record.type,
                "status": record.status,
                "source_file": record.source_file,
                "client_name": record.client_name,
                "email": record.email,
                "phone": record.phone,
                "company": record.company,
                "service_interest": record.service_interest,
                "amount": record.amount,
                "vat": record.vat,
                "total_amount": record.total_amount,
                "invoice_number": record.invoice_number,
                "priority": record.priority,
                "message": record.message,
                "email_type": record.email_type,
                "date_extracted": record.date_extracted.isoformat() if record.date_extracted else None
            }
            results.append(record_dict)
        
        return {
            "success": True,
            "total_records": len(results),
            "data": results
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/statistics")
async def get_statistics(db: Session = Depends(get_db)):
    """Get system statistics"""
    try:
        from database import ExtractedData
        
        all_records = db.query(ExtractedData).all()
        
        stats = {
            "total_records": len(all_records),
            "pending": len([r for r in all_records if r.status == "pending"]),
            "approved": len([r for r in all_records if r.status == "approved"]),
            "rejected": len([r for r in all_records if r.status == "rejected"]),
            "forms": len([r for r in all_records if r.type == "FORM"]),
            "emails": len([r for r in all_records if r.type == "EMAIL"]),
            "invoices": len([r for r in all_records if r.type == "INVOICE"]),
            "total_amount": sum(r.total_amount for r in all_records if r.total_amount and r.status == "approved"),
            "last_update": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    
    # Ensure required directories exist
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("exports", exist_ok=True)
    os.makedirs("static", exist_ok=True)
    
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)